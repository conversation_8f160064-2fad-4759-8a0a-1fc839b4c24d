"use client";

import { cn } from "../helpers/cn";
import { usePersistedTheme } from "../services/useThemePersistence";
import { ENV_TYPE } from "../app.constants";

type JobTypeFuzzerProps = {
  value: ENV_TYPE;
  onChange: (value: ENV_TYPE) => void;
  className?: string;
};

const jobTypeOptions = [
  { label: "Medusa", value: ENV_TYPE.MEDUSA },
  { label: "Echidna", value: ENV_TYPE.ECHIDNA },
  { label: "Foundry", value: ENV_TYPE.FOUNDRY },
  { label: "Halmos", value: ENV_TYPE.HALMOS },
  { label: "Kontrol", value: ENV_TYPE.KONTROL },
];

export const JobTypeFuzzer = ({
  value,
  onChange,
  className,
}: JobTypeFuzzerProps) => {
  const { isDark } = usePersistedTheme();

  return (
    <div
      className={cn(
        "flex flex-col gap-3 pb-4 border-b border-back-neutral-tertiary",
        className
      )}
    >
      <div className="flex flex-row items-stretch justify-stretch gap-2.5 self-stretch">
        <h3
          className={cn(
            "text-xl font-bold leading-[1.3] flex-1",
            isDark ? "text-[#F5F5F5]" : "text-black"
          )}
        >
          Select Job Type
        </h3>
      </div>

      <div className="flex flex-row flex-wrap gap-3">
        {jobTypeOptions.map((option) => {
          const isSelected = value === option.value;

          return (
            <button
              key={option.value}
              type="button"
              onClick={() => onChange(option.value)}
              className={cn(
                "flex flex-row justify-center items-center gap-2.5 px-1.5 py-1.5 rounded-lg transition-all duration-200",
                "text-sm font-black leading-[1.57]",
                isSelected
                  ? "bg-[#DFDBFA] text-[#5649B0]"
                  : isDark
                    ? "border border-white/75 text-white/75 hover:border-white hover:text-white"
                    : "border border-black/75 text-black/75 hover:border-black hover:text-black"
              )}
            >
              {option.label}
            </button>
          );
        })}
      </div>
    </div>
  );
};
