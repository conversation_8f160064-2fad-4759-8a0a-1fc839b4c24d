import type { <PERSON>ada<PERSON> } from "next";
import Link from "next/link";
import { FiArrowLeft } from "react-icons/fi";

import { AppHeader } from "../components/app-header";
import { GradientWrapper } from "../../components/gradient-wrapper";
import { H1, H3, Body2, Title2Strong } from "../../components/app-typography";

export const metadata: Metadata = {
  title: "Recon Free Tools",
  description: "A collection of tools for you to secure your smart contracts",
};

// Reusable ToolCard component
interface ToolCardProps {
  href: string;
  title: string;
  description?: string;
}

const ToolCard = ({ href, title, description }: ToolCardProps) => (
  <Link href={href} className="group block">
    <div className="rounded-lg border border-stroke-neutral-decorative bg-back-neutral-secondary p-6 transition-all duration-200 hover:border-accent-primary hover:bg-back-neutral-tertiary hover:shadow-lg">
      <H3 className="mb-2 text-fore-neutral-primary group-hover:text-accent-primary transition-colors duration-200">
        {title}
      </H3>
      {description && (
        <Body2
          color="secondary"
          className="group-hover:text-fore-neutral-primary transition-colors duration-200"
        >
          {description}
        </Body2>
      )}
    </div>
  </Link>
);

// Reusable ToolSection component
interface ToolSectionProps {
  title: string;
  children: React.ReactNode;
}

const ToolSection = ({ title, children }: ToolSectionProps) => (
  <div className="mb-8">
    <H3 className="mb-6 text-fore-neutral-primary">{title}</H3>
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {children}
    </div>
  </div>
);

function ToolsList() {
  return (
    <div className="w-full max-w-6xl">
      <div className="mb-8 text-center">
        <H1 className="mb-4 text-accent-primary">All Recon Tools</H1>
        <Title2Strong color="secondary" className="max-w-2xl mx-auto">
          A comprehensive collection of tools to help you secure your smart
          contracts and build robust invariant tests
        </Title2Strong>
      </div>

      <ToolSection title="Featured Tool">
        <ToolCard
          href="/tools/builder"
          title="Scaffold Invariants Sandbox"
          description="Build and test invariant testing suites with our interactive sandbox environment"
        />
      </ToolSection>

      <ToolSection title="Log Parsers">
        <ToolCard
          href="/tools/medusa"
          title="Medusa Log to Foundry"
          description="Convert Medusa fuzzing logs into Foundry test repros"
        />
        <ToolCard
          href="/tools/echidna"
          title="Echidna Log to Foundry"
          description="Transform Echidna logs into executable Foundry tests"
        />
        <ToolCard
          href="/tools/halmos"
          title="Halmos Log to Foundry"
          description="Parse Halmos symbolic execution logs for Foundry integration"
        />
      </ToolSection>

      <ToolSection title="Bytecode Tools">
        <ToolCard
          href="/tools/bytecode-compare"
          title="Bytecode Compare"
          description="Compare and analyze differences between EVM bytecode"
        />
        <ToolCard
          href="/tools/bytecode-to-interface"
          title="Bytecode to Interface"
          description="Reverse engineer Solidity interfaces from contract bytecode"
        />
        <ToolCard
          href="/tools/oracle-drift"
          title="Oracle Drift Calculator"
          description="Calculate oracle price drift and deviation thresholds"
        />
      </ToolSection>
    </div>
  );
}

export default function AllToolsPage() {
  return (
    <div className="main-container w-full overflow-x-hidden">
      <div className="relative z-10 min-h-screen">
        <AppHeader skipUser />

        <GradientWrapper className="flex min-h-[calc(100vh-80px)] items-center justify-center py-8">
          <div className="w-full max-w-6xl rounded-[20px] bg-back-neutral-tertiary px-[48px] py-[40px]">
            <div className="mb-8 text-left">
              <Link
                href="/"
                className="mb-6 inline-flex items-center text-fore-neutral-primary transition-colors duration-200 hover:text-fore-neutral-secondary"
              >
                <FiArrowLeft className="size-10" />
              </Link>
            </div>

            <ToolsList />
          </div>
        </GradientWrapper>
      </div>
    </div>
  );
}
