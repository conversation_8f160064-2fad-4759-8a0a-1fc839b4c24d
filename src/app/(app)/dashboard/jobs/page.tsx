"use client";

import axios from "axios";
import { useState } from "react";

import { ENV_TYPE } from "@/app/app.constants";
import { CreateJobForm } from "@/app/components/create-job-form/create-job-form";
import type { GitHubLinkFormValues } from "@/app/components/create-job-form/types";
import { Webhooks } from "@/app/components/Webhooks";
import { useGetJobs } from "@/app/services/jobs.hooks";
import { useJobSubmission } from "@/app/hooks/useJobSubmission";
import { useGetMyOrg } from "@/app/services/organization.hooks";
import config from "@/config";
import { checkFields } from "@/utils/fieldChecker";
import Link from "next/link";
import { Toaster, toast } from "react-hot-toast";
import { H1, H2, Body3 } from "@/app/components/app-typography";
import { AppButton } from "@/app/components/app-button";
import { AllJobs } from "./all-jobs";

export default function JobsPage() {
  const [env, setEnv] = useState(ENV_TYPE.MEDUSA);
  const [jobId, setJobId] = useState<null | number>(null);
  const [recipeId, setRecipeId] = useState<null | string>(null); // Track if the job was created from a recipe
  const [showDynamicReplacement, setShowDynamicReplacement] = useState(false);

  const { data: allJobs, refetch: refetchJobs } = useGetJobs();
  const { data: organization } = useGetMyOrg();

  // Use the job submission hook for dynamic replacement functionality
  const { getSubmissionHandler } = useJobSubmission({
    organization,
    allJobs,
    setJobId,
    refetchJobs,
  });

  const checkCanClone = async (repoName: string, orgName: string) => {
    try {
      await axios({
        method: "POST",
        url: `/api/jobs/canclone`,
        data: {
          orgName,
          repoName,
        },
      });
    } catch (e) {
      toast((t) => (
        <span className="flex w-[300px] flex-col items-center">
          Recon doesn't have access to this repository, please authorize the
          Recon app on the repo
          <Link href="/dashboard/installs" className="mt-2 text-black">
            Redirecting ...
          </Link>
        </span>
      ));
      setTimeout(() => {
        window.location.href = config.github.app.installationUrl;
      }, 3000);
      throw new Error("Can't clone this repo");
    }
  };

  const runningJobs = () => {
    return allJobs.filter(
      (job) =>
        job.status === "RUNNING" ||
        job.status === "STARTED" ||
        job.status === "QUEUED"
    );
  };
  const startEchidnaAbiJob = async ({
    pathToTester,
    echidnaConfig,
    contract,
    corpusDir,
    forkBlock,
    forkMode,
    forkReplacement,
    ref,
    repoName,
    rpcUrl,
    testLimit,
    testMode,
    preprocess,
    directory,
    orgName,
    targetCorpus,
    label,
  }: GitHubLinkFormValues) => {
    if (organization.billingStatus === "TRIAL" && runningJobs().length > 0) {
      alert(
        `You can only run one job at a time, currently running: ${runningJobs()
          .map((job) => `${job.label}-${job.id}`)
          .join(", ")}`
      );
      return;
    }
    try {
      await checkCanClone(repoName, orgName);
    } catch (err) {
      return;
    }

    const fuzzerArgs = {
      pathToTester,
      config: echidnaConfig,
      contract,
      corpusDir,
      forkBlock,
      forkMode,
      forkReplacement,
      rpcUrl,
      testLimit,
      testMode,
      preprocess,
      targetCorpus,
    };

    const areBasicFieldsOK = checkFields(orgName, repoName, ref);
    if (!areBasicFieldsOK) return;

    try {
      const foundData = await axios({
        method: "POST",
        url: `/api/jobs/echidna`,
        data: {
          directory,
          orgName,
          repoName,
          ref,
          fuzzerArgs,
          preprocess,
          label,
          recipeId,
        },
      });

      setJobId(foundData?.data?.data?.id as number);
      refetchJobs();
    } catch (e) {
      console.log("e", e);
      alert(`Something went wrong: ${e.response.data.message}`);
    }
  };

  const startFoundryJob = async ({
    //pathToTester,
    //echidnaConfig,
    contract,
    //corpusDir,
    forkBlock,
    forkMode,
    ref,
    repoName,
    //testLimit,
    //testMode,
    preprocess,
    directory,
    orgName,
    rpcUrl,
    runs,
    seed,
    verbosity,
    testCommand,
    testTarget,
    label,
    //targetCorpus,
  }: GitHubLinkFormValues) => {
    if (organization.billingStatus === "TRIAL" && runningJobs().length > 0) {
      alert(
        `You can only run one job at a time, currently running: ${runningJobs()
          .map((job) => `${job.label}-${job.id}`)
          .join(", ")}`
      );
      return;
    }
    try {
      await checkCanClone(repoName, orgName);
    } catch (err) {
      return;
    }

    const fuzzerArgs = {
      //pathToTester,
      //config: echidnaConfig,
      contract,
      //corpusDir,
      forkBlock,
      forkMode,
      //testLimit,
      //testMode,
      preprocess,
      //targetCorpus,
      rpcUrl,
      runs,
      seed,
      verbosity,
      testCommand,
      testTarget,
      recipeId,
    };

    const areBasicFieldsOK = checkFields(orgName, repoName, ref);
    if (!areBasicFieldsOK) return;

    try {
      const foundData = await axios({
        method: "POST",
        url: `/api/jobs/foundry`,
        data: {
          directory,
          orgName,
          repoName,
          ref,
          fuzzerArgs,
          preprocess,
          label,
        },
      });

      setJobId(foundData?.data?.data?.id as number);
      refetchJobs();
    } catch (e) {
      console.log("e", e);
      alert(`Something went wrong: ${e.response.data.message}`);
    }
  };

  const startHalmosJob = async ({
    //pathToTester,
    //echidnaConfig,
    contract,
    ref,
    repoName,
    preprocess,
    directory,
    orgName,
    halmosPrefix,
    halmosArray,
    halmosLoops,
    verbosity,
    label,
  }: GitHubLinkFormValues) => {
    if (organization.billingStatus === "TRIAL" && runningJobs().length > 0) {
      alert(
        `You can only run one job at a time, currently running: ${runningJobs()
          .map((job) => `${job.label}-${job.id}`)
          .join(", ")}`
      );
      return;
    }
    try {
      await checkCanClone(repoName, orgName);
    } catch (err) {
      return;
    }

    const fuzzerArgs = {
      contract,
      preprocess,
      halmosArray,
      halmosLoops,
      halmosPrefix,
      verbosity,
    };

    try {
      const foundData = await axios({
        method: "POST",
        url: `/api/jobs/halmos`,
        data: {
          directory,
          orgName,
          repoName,
          ref,
          fuzzerArgs,
          preprocess,
          label,
          recipeId,
        },
      });

      setJobId(foundData?.data?.data?.id as number);
      refetchJobs();
    } catch (e) {
      console.log("e", e);
      alert("Something went wrong");
    }
  };

  const startKontrolJob = async ({
    //pathToTester,
    //echidnaConfig,
    contract,
    ref,
    repoName,
    preprocess,
    directory,
    orgName,
    kontrolTest,
    label,
  }: GitHubLinkFormValues) => {
    if (organization.billingStatus === "TRIAL" && runningJobs().length > 0) {
      alert(
        `You can only run one job at a time, currently running: ${runningJobs()
          .map((job) => `${job.label}-${job.id}`)
          .join(", ")}`
      );
      return;
    }
    try {
      await checkCanClone(repoName, orgName);
    } catch (err) {
      return;
    }

    const fuzzerArgs = {
      //pathToTester,
      //config: echidnaConfig,
      contract,
      //testLimit,
      //testMode,
      preprocess,
      //targetCorpus,
      kontrolTest,
    };

    try {
      const foundData = await axios({
        method: "POST",
        url: `/api/jobs/kontrol`,
        data: {
          directory,
          orgName,
          repoName,
          ref,
          fuzzerArgs,
          preprocess,
          label,
          recipeId,
        },
      });

      setJobId(foundData?.data?.data?.id as number);
      refetchJobs();
    } catch (e) {
      console.log("e", e);
      alert("Something went wrong");
    }
  };

  const startMedusaAbiJob = async ({
    orgName,
    repoName,
    ref,
    directory,
    medusaConfig,
    timeout,
    preprocess,
    targetCorpus,
    label,
  }: GitHubLinkFormValues) => {
    if (organization.billingStatus === "TRIAL" && runningJobs().length > 0) {
      alert(
        `You can only run one job at a time, currently running: ${runningJobs()
          .map((job) => `${job.label}-${job.id}`)
          .join(", ")}`
      );
      return;
    }
    try {
      await checkCanClone(repoName, orgName);
    } catch (err) {
      return;
    }

    const fuzzerArgs = {
      timeout,
      config: medusaConfig,
      targetCorpus,
    };

    const areBasicFieldsOK = checkFields(orgName, repoName, ref);
    if (!areBasicFieldsOK) return;

    try {
      const foundData = await axios({
        method: "POST",
        url: `/api/jobs/medusa`,
        data: {
          fuzzerArgs,
          preprocess,
          orgName,
          repoName,
          ref,
          directory,
          label,
          recipeId,
        },
      });

      setJobId(foundData?.data?.data?.id as number);
      refetchJobs();
    } catch (e) {
      alert(`Something went wrong: ${e.response.data.message}`);
    }
  };

  // Choose the appropriate submission handler based on mode
  const onSubmit = showDynamicReplacement
    ? getSubmissionHandler(env)
    : env === ENV_TYPE.MEDUSA
      ? startMedusaAbiJob
      : env === ENV_TYPE.ECHIDNA
        ? startEchidnaAbiJob
        : env === ENV_TYPE.FOUNDRY
          ? startFoundryJob
          : env === ENV_TYPE.HALMOS
            ? startHalmosJob
            : startKontrolJob;

  return (
    <div className="min-h-screen bg-back-neutral-secondary dark:bg-back-neutral-primary">
      <div className="mx-auto max-w-7xl px-6 py-12">
        <Toaster position="top-center" reverseOrder={false} />
        <Webhooks />

        <div className="mb-8">
          <H1 className="mb-6 text-accent-primary">Jobs</H1>

          {/* Toggle for Dynamic Replacement */}
          <div className="mb-6 flex items-center gap-4">
            <Body3 color="secondary" className="font-semibold">
              Enable Dynamic Replacement:
            </Body3>
            <AppButton
              variant={showDynamicReplacement ? "primary" : "secondary"}
              size="sm"
              onClick={() => setShowDynamicReplacement(!showDynamicReplacement)}
            >
              {showDynamicReplacement ? "Enabled" : "Disabled"}
            </AppButton>
          </div>

          {/* Dynamic Replacement Warning */}
          {showDynamicReplacement && (
            <div className="mb-6 rounded-lg border border-status-warning bg-status-warning/10 p-4">
              <H2 className="mb-2" color="primary">
                Dynamic Replacement Mode
              </H2>
              <div className="space-y-2">
                <Body3 color="secondary">
                  • Dynamic Replacement is in EXPERIMENTAL mode
                </Body3>
                <Body3 color="secondary">
                  • All variables Dynamically Replaced MUST be in the
                  `Setup.sol` file
                </Body3>
                <Body3 color="secondary">
                  • Make sure you have no clashing file!
                </Body3>
              </div>
            </div>
          )}
        </div>

        <div className="mb-8">
          <CreateJobForm
            title=""
            submitLabel={showDynamicReplacement ? "Run Job" : "Start job now"}
            {...{
              env,
              jobId,
              setEnv,
            }}
            onSubmit={onSubmit}
            setRecipeId={setRecipeId}
            dynamicReplacement={showDynamicReplacement}
          />
        </div>

        <AllJobs />
      </div>
    </div>
  );
}
